# AI Request Interface - Image Support

This document demonstrates how to use the new `filePath()` method in the `AiRequestInterface` to include images in AI requests.

## Overview

The `AiRequestInterface` now supports including image files in AI requests through the `filePath()` method. When an image path is provided, the `OpenAiRequestRunner` will:

1. Read the image file from the specified path
2. Encode it as base64
3. Format it according to OpenAI's vision API requirements
4. Include both text and image content in the request

## Interface Changes

### AiRequestInterface

```php
interface AiRequestInterface
{
    // ... existing methods ...
    
    /**
     * Get the file path for an image to be included in the AI request.
     * Used for vision-enabled AI models to analyze images.
     * 
     * @return string|null The absolute path to the image file, or null if no image is needed
     */
    public function filePath(): ?string;
}
```

## Implementation Example

```php
<?php

namespace App\Services\AI;

use App\Models\Interfaces\AiLoggableInterface;
use App\Models\File;

class ImageAnalysisRequest implements AiRequestInterface
{
    public function __construct(
        public File $file,
        public ?string $imagePath = null
    ) {}

    public function getSystemPrompt(): string
    {
        return 'You are an AI assistant that can analyze images and extract relevant information.';
    }

    public function getUserPrompt(): string
    {
        return 'Please analyze the provided image and extract any text, objects, or relevant information you can identify.';
    }

    public function getResponseFormat(): array
    {
        return [
            'type' => 'json_schema',
            'json_schema' => [
                'name' => 'image_analysis',
                'schema' => [
                    'type' => 'object',
                    'properties' => [
                        'description' => [
                            'type' => 'string',
                            'description' => 'A description of what was found in the image.',
                        ],
                        'extracted_text' => [
                            'type' => ['string', 'null'],
                            'description' => 'Any text found in the image.',
                        ],
                        'objects' => [
                            'type' => 'array',
                            'items' => ['type' => 'string'],
                            'description' => 'List of objects identified in the image.',
                        ],
                    ],
                    'required' => ['description'],
                    'additionalProperties' => false,
                ],
                'strict' => true,
            ]
        ];
    }

    public function getAiLoggable(): AiLoggableInterface
    {
        return $this->file;
    }

    public function updateDataAndGetFields(array $responseData): array
    {
        // Update your model with the extracted data
        // Return the fields that were updated
        return ['description', 'extracted_text', 'objects'];
    }

    public function filePath(): ?string
    {
        return $this->imagePath;
    }
}
```

## Usage

```php
// Create an AI request with an image
$imagePath = '/path/to/your/image.jpg';
$file = File::find(1); // Your file model
$request = new ImageAnalysisRequest($file, $imagePath);

// Run the AI request
$runner = new OpenAiRequestRunner($request);
$runner->run();
```

## Supported Image Formats

The implementation supports the following image formats:
- JPEG (`image/jpeg`)
- PNG (`image/png`)
- GIF (`image/gif`)
- WebP (`image/webp`)

## Backward Compatibility

Existing implementations of `AiRequestInterface` need to add the `filePath()` method. For implementations that don't use images, simply return `null`:

```php
public function filePath(): ?string
{
    return null;
}
```

## Error Handling

The system handles various error conditions:
- If `filePath()` returns `null`, the request proceeds as text-only
- If the file path doesn't exist, the request falls back to text-only
- If the file cannot be read, an exception is thrown
- If the image format is unsupported, an exception is thrown

## OpenAI API Format

When an image is provided, the user message is formatted as:

```json
{
    "role": "user",
    "content": [
        {
            "type": "text",
            "text": "Your user prompt text"
        },
        {
            "type": "image_url",
            "image_url": {
                "url": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQ..."
            }
        }
    ]
}
```

This follows OpenAI's vision API specification for including images in chat completions.
