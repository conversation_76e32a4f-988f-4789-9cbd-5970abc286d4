<?php

namespace App\Services\AI;

use App\Helpers;
use App\Models\Website;
use OpenAI\Responses\Chat\CreateResponse;

class OpenAiRequestRunner
{
    use HasOpenAIClient;

    public function __construct(public ?AiRequestInterface $aiRequest = null, public ?AiRequestTriggerEnum $trigger = AiRequestTriggerEnum::AUTOMATIC) {}

    public function setAiRequest(AiRequestInterface $aiRequest): self
    {
        $this->aiRequest = $aiRequest;

        return $this;
    }

    public function getAiRequestTrigger(): AiRequestTriggerEnum
    {
        return $this->trigger;
    }

    public function setAiRequestTrigger(AiRequestTriggerEnum $trigger): self
    {
        $this->trigger = $trigger;

        return $this;
    }

    protected function runAIRequest(array $aiParams): CreateResponse
    {
        return $this->getClient()->chat()->create($aiParams);
    }

    /**
     * @throws \Exception
     */
    public function run(): self
    {
        if (!$this->aiRequest) {
            throw new \Exception('AI request is not set');
        }

        $website = Helpers::getCurrentWebsite();
        if (!$website->features[Website::FEATURE_ENABLE_AI]) {
            throw new \Exception('AI usage is not enabled');
        }

        $aiLog = $this->aiRequest->getAiLoggable()->aiLogs()->create([
            'ai_function' => get_class($this->aiRequest),
            'trigger' => $this->getAiRequestTrigger()->value,
            'trigger_user_id' => auth()->user()?->id ?? null,
        ]);

        $aiParams = $this->getAiParams();

        info('Running AI request', $aiParams);

        $aiLog->update([
            'ai_input' => $aiParams,
        ]);

        $response = $this->runAIRequest($aiParams);

        $aiOutput = $response->toArray();

        info('AI request output:', $aiOutput);

        $aiLog->update([
            'ai_output' => $aiOutput,
        ]);

        $responseData = $this->extractData($response);

        $aiLog->update([
            'data' => $responseData,
        ]);

        $fields = $this->aiRequest->updateDataAndGetFields($responseData);

        $aiLog->update([
            'fields' => $fields,
        ]);

        return $this;
    }

    public function getAiParams(): array
    {
        $systemPrompt = $this->aiRequest->getSystemPrompt();
        $userPrompt = $this->aiRequest->getUserPrompt();
        $filePath = $this->aiRequest->filePath();

        $userMessage = [
            'role' => 'user',
        ];

        // If there's a file path, create a multi-content message with text and image
        if ($filePath && file_exists($filePath)) {
            $imageData = $this->encodeImageToBase64($filePath);
            $userMessage['content'] = [
                [
                    'type' => 'text',
                    'text' => $userPrompt,
                ],
                [
                    'type' => 'image_url',
                    'image_url' => [
                        'url' => $imageData,
                    ],
                ],
            ];
        } else {
            // Standard text-only message
            $userMessage['content'] = $userPrompt;
        }

        return [
            'model' => $this->getDefaultModel(),
            'temperature' => 0.4,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => $systemPrompt,
                ],
                $userMessage,
            ],
            'response_format' => $this->aiRequest->getResponseFormat(),
        ];
    }

    public function extractData(CreateResponse $response): array
    {
        $responseContent = $response->choices[0]->message->content;

        return json_decode($responseContent, true);
    }
}
